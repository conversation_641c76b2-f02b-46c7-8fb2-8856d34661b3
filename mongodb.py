#!/usr/bin/python3
# -*- coding: UTF-8 -*-

from pymongo import MongoClient

# 连接MongoDB
client = MongoClient('********************************************************************************************')

# 获取所有数据库
databases = client.list_database_names()

# 排除系统数据库
system_dbs = ['admin', 'config', 'local']
user_dbs = [db for db in databases if db not in system_dbs]

print(f"找到以下用户数据库: {user_dbs}")

# 要清空的集合名称
collections_to_clear = ['BetHistory', 'players', 'rawSpinData']

# 遍历每个数据库并清空指定集合
for db_name in user_dbs:
    db = client[db_name]
    
    # 获取数据库中的所有集合
    db_collections = db.list_collection_names()
    
    for collection_name in collections_to_clear:
        if collection_name in db_collections:
            # 清空集合
            result = db[collection_name].delete_many({})
            print(f"已清空 {db_name}.{collection_name} 集合，删除了 {result.deleted_count} 条记录")
        else:
            print(f"{db_name} 数据库中不存在 {collection_name} 集合")

print("清空操作完成")

